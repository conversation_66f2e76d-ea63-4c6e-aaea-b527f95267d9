
class AppSizes {
  // Padding and margin sizes
  static const double xs = 4;
  static const double xs2=6;
  static const double sm = 8;
  static const double sm2 = 12;
  static const double md = 16;
  static const double md2 = 20;
  static const double lg = 24;
  static const double xl = 32;

  // Default spacing between sections
  static const double spaceExtraSmall = 4;
  static const double spaceSmall = 8;
  static const double spaceBtwList = 12;
  static const double spaceBtwItems = 16;
  static const double defaultSpace = 24;
  static const double spaceBtwSections = 32;

  // Input field
  static const double inputFieldRadius = 8;
  static const double inputFieldRadius2 = 12;
  static const double spaceBtwInputFields = 16;

  // Container Radius
  // Input field
  static const double containerRadius = 8;
  static const double containerRadius2 = 12;

  // Icon sizes
  static const double iconXs = 12;
  static const double iconSm = 16;
  static const double iconSm2 = 20;
  static const double iconMd = 24;
  static const double iconLg = 32;
  static const double iconXl = 36;

  // Font sizes
  static const double fontSizeSm = 14;
  static const double fontSizeMd = 16;
  static const double fontSizeLg = 18;

  // Button sizes
  static const double buttonPadding = 8;
  static const double buttonRadius = 16;
  static const double buttonWidth = 120;
  static const double buttonElevation = 4;

  // AppBar height
  static const double appBarHeight = 56;

  // Image sizes
  static const double imageThumbSize = 80;

  // Border radius
  static const double borderRadiusSm = 8;
  static const double borderRadiusMd = 12;
  static const double borderRadiusLg = 16;

  // Divider height
  static const double dividerHeight = 1;

  // Card sizes
  static const double cardElevation = 2;
  static const double cardRadiusXs = 6;
  static const double cardRadiusSm = 10;
  static const double cardRadiusMd = 12;
  static const double cardRadiusLg = 16;

  // Image carousel height
  static const double imageCarouselHeight = 200;

  // Loading indicator size
  static const double loadingIndicatorSize = 36;

  // Grid view spacing
  static const double gridViewSpacing = 16;

  // ========================================
  // M3 BORDER RADIUS SYSTEM (KEEP M3 IMPROVEMENTS)
  // ========================================

  /// M3 Border Radius Scale
  static const double radiusNone = 0.0;
  static const double radiusXs = 4.0;    // Small components
  static const double radiusSm = 8.0;    // Small components
  static const double radiusMd = 12.0;   // Medium components
  static const double radiusLg = 16.0;   // Medium components
  static const double radiusXl = 20.0;   // Large components
  static const double radiusXxl = 24.0;  // Large components
  static const double radiusXxxl = 28.0; // Large components
  static const double radiusFull = 999.0; // Fully rounded

  // ========================================
  // M3 COMPONENT SIZES (KEEP M3 IMPROVEMENTS)
  // ========================================

  /// M3 Touch Target Sizes (Minimum 48dp for accessibility)
  static const double touchTargetMin = 48.0;
  static const double touchTargetRecommended = 56.0;
  static const double touchTargetLarge = 64.0;

  /// M3 Button Heights
  static const double buttonSmall = 36.0;
  static const double buttonMedium = 48.0;
  static const double buttonLarge = 56.0;

  /// M3 Form Field Heights
  static const double formFieldSmall = 40.0;
  static const double formFieldMedium = 48.0;
  static const double formFieldLarge = 56.0;

  // Legacy aliases for backward compatibility
  static const double minTouchTarget = touchTargetMin;
  static const double recommendedTouchTarget = touchTargetRecommended;
  static const double largeTouchTarget = touchTargetLarge;

  // ========================================
  // M3 TYPOGRAPHY SIZES (KEEP M3 IMPROVEMENTS)
  // ========================================

  /// M3 Typography Scale
  static const double textDisplayLarge = 57.0;
  static const double textDisplayMedium = 45.0;
  static const double textDisplaySmall = 36.0;
  static const double textHeadlineLarge = 32.0;
  static const double textHeadlineMedium = 28.0;
  static const double textHeadlineSmall = 24.0;
  static const double textTitleLarge = 22.0;
  static const double textTitleMedium = 16.0;
  static const double textTitleSmall = 14.0;
  static const double textBodyLarge = 16.0;
  static const double textBodyMedium = 14.0;
  static const double textBodySmall = 12.0;
  static const double textLabelLarge = 14.0;
  static const double textLabelMedium = 12.0;
  static const double textLabelSmall = 11.0;

  // ========================================
  // M3 ELEVATION & SHADOWS (KEEP M3 IMPROVEMENTS)
  // ========================================

  /// M3 Elevation Scale
  static const double elevationLevel0 = 0.0;
  static const double elevationLevel1 = 1.0;
  static const double elevationLevel2 = 3.0;
  static const double elevationLevel3 = 6.0;
  static const double elevationLevel4 = 8.0;
  static const double elevationLevel5 = 12.0;
}



