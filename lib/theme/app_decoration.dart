import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';


/// Material Design 3 Compliant Decorations
class AppDecoration {
  /// M3 Outline decorations using theme colors
  static BoxDecoration outlineOnPrimaryContainer(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      color: theme.colorScheme.surfaceContainerHighest, // M3 surface container
      border: Border.all(
        color: theme.colorScheme.primary.withValues(alpha: 0.5), // M3 primary variant
        width: 1,
        strokeAlign: strokeAlignOutside,
      ),
      borderRadius: BorderRadiusStyle.radiusMd, // M3 border radius
      boxShadow: [
        BoxShadow(
          color: theme.colorScheme.shadow.withValues(alpha: 0.1), // M3 shadow
          spreadRadius: 1,
          blurRadius: AppSizes.elevationLevel2,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  static BoxDecoration outlinePrimary(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      color: theme.colorScheme.surface, // M3 surface color
      border: Border.all(
        color: theme.colorScheme.primary, // M3 primary color
        width: 1,
        strokeAlign: strokeAlignOutside,
      ),
      borderRadius: BorderRadiusStyle.radiusSm, // M3 border radius
    );
  }

  static BoxDecoration outlineGrey(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      color: theme.colorScheme.surface, // M3 surface color
      border: Border.all(
        color: theme.colorScheme.outline.withValues(alpha: 0.5), // M3 outline color
        width: 1,
        strokeAlign: BorderSide.strokeAlignCenter,
      ),
      borderRadius: BorderRadiusStyle.radiusSm, // M3 border radius
      boxShadow: m3Shadow1(theme),
    );
  }

  /// M3 Shadow system using theme colors
  static List<BoxShadow> m3Shadow1(ThemeData theme) => [
        BoxShadow(
          color: theme.colorScheme.shadow.withValues(alpha: 0.1), // M3 shadow color
          spreadRadius: 0,
          blurRadius: AppSizes.elevationLevel2,
          offset: const Offset(0, 1),
        ),
      ];

  static List<BoxShadow> m3Shadow2(ThemeData theme) => [
        BoxShadow(
          color: theme.colorScheme.shadow.withValues(alpha: 0.15), // M3 shadow color
          spreadRadius: 0,
          blurRadius: AppSizes.elevationLevel3,
          offset: const Offset(0, 2),
        ),
      ];

  static List<BoxShadow> m3Shadow3(ThemeData theme) => [
        BoxShadow(
          color: theme.colorScheme.shadow.withValues(alpha: 0.2), // M3 shadow color
          spreadRadius: 1,
          blurRadius: AppSizes.elevationLevel4,
          offset: const Offset(0, 2),
        ),
      ];

  // Legacy shadows for backward compatibility
  static List<BoxShadow> get shadow1_1 => const [
        BoxShadow(
          color: Color(0x0A000000),
          spreadRadius: 0,
          blurRadius: 3,
          offset: Offset(0, 2),
        ),
      ];

  static List<BoxShadow> get shadow1_2 => const [
        BoxShadow(
          color: Color(0x38414A26),
          spreadRadius: 0,
          blurRadius: 2,
          offset: Offset(0, 1),
        ),
      ];

  static List<BoxShadow> get shadow1_3 => [
        const BoxShadow(
          color: AppColors.shadow1,
          blurRadius: 4,
          offset: Offset(0, 0.5),
        ),
      ];

  /// M3 Fill decorations using theme colors
  static BoxDecoration fillSurface(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surface, // M3 surface color
    );
  }

  static BoxDecoration fillSurfaceContainer(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceContainer, // M3 surface container
    );
  }

  static BoxDecoration fillSurfaceContainerHighest(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceContainerHighest, // M3 surface container highest
    );
  }

  /// Legacy fill decorations for backward compatibility
  static BoxDecoration get fillDeepOrange => const BoxDecoration(
        color: AppColors.background3,
      );

  static BoxDecoration get fillWhite => const BoxDecoration(
        color: AppColors.white,
      );

  static BoxDecoration get fillBG2 => const BoxDecoration(
        color: AppColors.background2,
      );
}

/// Material Design 3 Compliant Border Radius Styles
class BorderRadiusStyle {
  // M3 Small Border Radius (4-8dp)
  static BorderRadius get radiusXs => BorderRadius.circular(AppSizes.radiusXs);
  static BorderRadius get radiusSm => BorderRadius.circular(AppSizes.radiusSm);

  // M3 Medium Border Radius (12-16dp)
  static BorderRadius get radiusMd => BorderRadius.circular(AppSizes.radiusMd);
  static BorderRadius get radiusLg => BorderRadius.circular(AppSizes.radiusLg);

  // M3 Large Border Radius (20-28dp)
  static BorderRadius get radiusXl => BorderRadius.circular(AppSizes.radiusXl);
  static BorderRadius get radiusXxl => BorderRadius.circular(AppSizes.radiusXxl);
  static BorderRadius get radiusXxxl => BorderRadius.circular(AppSizes.radiusXxxl);

  // Special cases
  static BorderRadius get radiusFull => BorderRadius.circular(AppSizes.radiusFull);
  static BorderRadius get radiusNone => BorderRadius.circular(AppSizes.radiusNone);

  // Legacy aliases for backward compatibility
  static BorderRadius get radius8 => radiusSm;
  static BorderRadius get border12 => radiusMd;
  static BorderRadius get border16 => radiusLg;

  // M3 Specific patterns
  static BorderRadius get topOnly => BorderRadius.vertical(
        top: Radius.circular(AppSizes.radiusXl),
      );
  static BorderRadius get bottomOnly => BorderRadius.vertical(
        bottom: Radius.circular(AppSizes.radiusXl),
      );
  static BorderRadius get leftOnly => BorderRadius.horizontal(
        left: Radius.circular(AppSizes.radiusXl),
      );
  static BorderRadius get rightOnly => BorderRadius.horizontal(
        right: Radius.circular(AppSizes.radiusXl),
      );

  // Legacy compatibility
  static BorderRadius get border32 => BorderRadius.vertical(
        top: Radius.circular(AppSizes.xl), // Reverted to use original xl value (32)
      );
}

// Comment/Uncomment the below code based on your Flutter SDK version.
// For Flutter SDK Version 3.7.2 or greater.

double get strokeAlignOutside => BorderSide.strokeAlignOutside;

// For Flutter SDK Version 3.7.1 or less.

// // StrokeAlign get strokeAlignOutside => StrokeAlign.outside;
