import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/signinOption_screen.dart';
import '../../../api/data_store.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/icon_constants.dart';
import '../../../utils/constants/sizes.dart';
import '../../widgets/custom_image_view.dart';
import 'dentist_homescreen.dart';
import 'dentist_orders_screen.dart';
import 'dentist_profilescreen.dart';
import 'dentist_reports_screen.dart';

class DentistBottomBar extends StatefulWidget {
  final int initialIndex;
  final bool isEmailVerified;
  final int? initialOrderTab;

  const DentistBottomBar({
    super.key,
    this.initialIndex = 0,
    this.isEmailVerified = false,
    this.initialOrderTab,
  });

  @override
  _DentistBottomBarState createState() => _DentistBottomBarState();
}

class _DentistBottomBarState extends State<DentistBottomBar> {
  late int _selectedIndex;
  late int? _initialOrderTab; // Store locally to clear after first use
  String? token = getData.read('token');


  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
    _initialOrderTab = widget.initialOrderTab;
  }

  void _onItemTapped(int index) {
    final bool isTokenValid = token != null && token!.isNotEmpty;
    _selectedIndex = index;
    // Show SigninOptionScreen ONLY if token is invalid AND not on Home
    if (!isTokenValid && _selectedIndex != 0) {
      AppUtils.showToastMessage('Please Sign in to Continue');
      Get.to(() => const SigninOptionScreen());
      return;
    }else{
      setState(() {
        // Clear initialOrderTab after navigating to Orders once
        if (index == 1) _initialOrderTab = null;
      });
    }

  }

  Widget _getScreen(int index) {
    switch (index) {
      case 0:
        return const DentistHomeScreen();
      case 1:
        return DentistOrdersScreen(initialTab: _initialOrderTab ?? 0);
      case 2:
        return const DentistReportsScreen();
      case 3:
        return const DentistProfilescreen();
      default:
        return const DentistHomeScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      backgroundColor: AppColors.white,
      body:  _getScreen(_selectedIndex),
      bottomNavigationBar: SizedBox(
        height: 90,
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppSizes.borderRadiusLg),
              topRight: Radius.circular(AppSizes.borderRadiusLg),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.16),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppSizes.borderRadiusLg),
              topRight: Radius.circular(AppSizes.borderRadiusLg),
            ),
            child: BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              elevation: AppSizes.elevationLevel2, // M3 elevation
              backgroundColor: Theme.of(context).colorScheme.surface, // M3 surface color
              items: [
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.homeOutlined,
                    width: AppSizes.iconMd, // M3 icon size
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.home,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.ordersOutlined,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.orders,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Orders',
                ),
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.reportsOutlined,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.reports,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Reports',
                ),
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.profileOutlined,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.profile,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Profile',
                ),
              ],
              currentIndex: _selectedIndex,
              selectedItemColor: Theme.of(context).colorScheme.primary, // M3 primary color
              unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6), // M3 unselected color
              selectedLabelStyle: Theme.of(context).textTheme.labelMedium?.copyWith( // M3 typography
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: Theme.of(context).textTheme.labelMedium, // M3 typography
              onTap: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }
}
