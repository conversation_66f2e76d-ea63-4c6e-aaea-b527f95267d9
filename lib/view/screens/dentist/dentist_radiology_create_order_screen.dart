import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/controllers/dentist_controllers/dentist_service_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';

import '../../../data/models/dentist/dentist_home_data_model.dart';
import '../../../data/models/dentist/doctor_service_info.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';
import 'dentist_checkout_screen.dart';

class DentistRadiologyCreateorderScreen extends StatefulWidget {
  const DentistRadiologyCreateorderScreen({super.key});

  @override
  State<DentistRadiologyCreateorderScreen> createState() => _DentistRadiologyCreateorderScreenState();
}

class _DentistRadiologyCreateorderScreenState extends State<DentistRadiologyCreateorderScreen> {
  String ? selectedValue ;
  final DoctorServiceController doctorServiceController = Get.find();
  final PermissionService _permissionService = PermissionService();

  final TextEditingController doctorNameController = TextEditingController();
  final TextEditingController hospitalNameController = TextEditingController();
  final TextEditingController labNameController = TextEditingController();
  final TextEditingController toothNameController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController patientIdController = TextEditingController();
  final TextEditingController reasonForScanController = TextEditingController();

  String? selectedvalue;
  String? selectedShade;

  String? selectedLaboratory;
  String? selectedRadiologySupplierId; // Store the selected Radiology ID


  List<dynamic> filteredServices = [];
  List<Choice<dynamic>> selectedServices = []; // Holds selected services
  //List<String> selectedServices = [];
  List<String> items = ['white', 'yellow'];
  late RxList<DoctorService> services;

  String searchQuery = "";
  bool _showServiceError = false;

  bool _orderDateError = false;
  bool _requiredDateError = false;

  bool showDateError = false;
  bool showServicesError = false;
  bool showShadeError = false;

  final GlobalKey<FormState> _key = GlobalKey<FormState>();
  DateTime? orderDate;
  DateTime? requiredDate;

  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected, String label, bool showError, {bool onlyToday = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: () => _selectDate(context, onDateSelected, onlyToday: onlyToday),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null ? DateFormat('dd-MM-yyyy').format(date) : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2.copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }


  Future<void> _selectDate(BuildContext context, Function(DateTime) onDateSelected, {bool onlyToday = false}) async {
    final DateTime today = DateTime.now();
    final DateTime onlyTodayDate = DateTime(today.year, today.month, today.day);

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: onlyToday ? onlyTodayDate : today,
      firstDate: onlyToday ? onlyTodayDate : today,  // ✅ Restrict selection to today if onlyToday is true
      lastDate: onlyToday ? onlyTodayDate : DateTime(2100),  // ✅ Allow only today if onlyToday is true
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary, // Change the primary color
              onPrimary: Colors.white, // Text color on primary color
              onSurface: AppColors.black, // Text color on surface
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.white, // White button text color
                backgroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }


  @override
  void dispose() {
    doctorNameController.dispose();
    hospitalNameController.dispose();
    labNameController.dispose();
    toothNameController.dispose();
    remarksController.dispose();
    patientIdController.dispose();
    patientNameController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    if (doctorServiceController.service.isNotEmpty) {
      log("Service list initialized with ${doctorServiceController.service.length} items");
    } else {
      log("Service list is empty");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: _permissionService.hasAnyPermission('radiology_centers', ['is_add', 'is_view']),
      child: Scaffold(
        appBar: const CustomAppBar(
          title: 'Create Order',
          textColor: Colors.white,
          backgroundColor: AppColors.primary,
        ),
        body: GetBuilder<HomeController>(
          builder: (homeController) {
            final radiologyData = homeController.dentistHomeDataModel?.radiology;

            return SafeArea(
              child: GestureDetector(
                onTap: FocusScope.of(context).unfocus,
                child: Center(
                  child: SingleChildScrollView(
                    child: Form(
                      key: _key,
                      child:  Container(
                          color: Colors.white,
                        width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              Padding(
                                padding: const EdgeInsets.all(AppSizes.md),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const TextFieldLabel(
                                      label: 'Select organization',
                                    ),
                                    const SizedBox(height: AppSizes.spaceSmall,),
                                    CustomTextFormField(
                                      allowShadow: true,
                                      hintStyle: const TextStyle(
                                          color: AppColors.darkerGrey
                                      ),
                                      hintText: 'Radiology',
                                      filled: true,
                                      fillColor: AppColors.grey,
                                      enabled: false,
                                      borderDecoration: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius: BorderRadiusStyle.border12
                                      ),
                                    ),
                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),

                                    LabelTextField(
                                        label: 'Patient name',
                                        hint: 'Enter Patient Name',
                                        controller: patientNameController,
                                        validator: AppValidators.validateText),
                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),
                                    LabelTextField(
                                        label: 'Patient Id',
                                        hint: 'Enter Patient Id',
                                        controller: patientIdController,
                                        validator: AppValidators.validateText),
                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),
                                    Visibility(
                                      visible: _permissionService.hasPermission('radiology_centers', 'is_list'),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const TextFieldLabel(
                                            label: 'Radiology Center Name',
                                          ),
                                          const SizedBox(height: AppSizes.spaceSmall,),
                                          CustomDropdown(
                                            dropdownWidth: (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5)-30 ,
                                            hintText: 'Select Radiology center name',
                                            items: radiologyData != null ? radiologyData.where((lab) => lab.isRaiseOrders == true).map((lab) => lab.name).toList() : [],
                                            onChanged: (String? value) {
                                              setState(() {
                                                selectedLaboratory = value;

                                                // Find the selected laboratory and get its services
                                                final selectedLab = radiologyData?.firstWhere(
                                                      (lab) => lab.name == value,

                                                );
                                                selectedRadiologySupplierId =selectedLab?.id;

                                                filteredServices = selectedLab?.services ?? [];
                                              });
                                            },
                                          ),
                                          const SizedBox(
                                            height: AppSizes.defaultSpace,
                                          ),
                                          const TextFieldLabel(label: 'Service Name'),

                                          MultiSelectField<dynamic>(
                                            label: "Select Services",
                                            textStyleLabel: CustomTextStyles.b4_1.copyWith(color: AppColors.darkGrey),
                                            data: () => filteredServices
                                                .map<Choice<dynamic>>(
                                                  (service) => Choice<dynamic>(service.id.toString(), service.servicename, metadata: service),
                                            )
                                                .toList(),
                                            onSelect: (List<Choice<dynamic>> selectedChoice, bool isFromDefaultData) {
                                              setState(() {
                                                selectedServices = selectedChoice; // Update selected services
                                                _showServiceError = false; // Remove error when a service is selected
                                              });
                                            },
                                            defaultData: selectedServices, // Use selectedServices list
                                            cleanCurrentSelection: false,
                                            singleSelection: false,
                                            useTextFilter: true,
                                            itemMenuStyle: CustomTextStyles.b4_1,
                                            menuStyle: MenuStyle(
                                              shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
                                              backgroundColor: const WidgetStatePropertyAll(Colors.white),
                                              maximumSize:  WidgetStatePropertyAll<Size>(
                                                Size(Get.size.width, 200), // Set width and height manually
                                              ),
                                            ),
                                            itemColor: ItemColor(
                                              selected: Colors.purple.withOpacity(0.2),
                                              unSelected: Colors.white,
                                            ),
                                            isDisabled: selectedLaboratory == null, // Disable if no lab is selected
                                          ),
                                        ],
                                      ),
                                    ),

                                    if (showServicesError)
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: AppSizes.sm, left: AppSizes.md),
                                        child: Text(
                                          "Please Select At least One Service",
                                          style: CustomTextStyles.b6.copyWith(
                                            color: Colors.redAccent,
                                          ),
                                        ),
                                      ),
                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),
                                    const TextFieldLabel(
                                      label: 'Reason for Scan',
                                      isMandatory: true,
                                    ),
                                    CustomTextFormField(
                                      maxLines: 3,
                                      hintText: 'Write Note...',
                                      controller: reasonForScanController,

                                    ),

                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),
                                    _buildDateField(orderDate, (date) {
                                      setState(() {
                                        orderDate = date;
                                        _orderDateError = false;
                                      });
                                    },
                                        'Order Date', _orderDateError,onlyToday: true),
                                    const SizedBox(height: AppSizes.defaultSpace),
                                    _buildDateField(requiredDate, (date) {
                                      requiredDate = date;
                                      _requiredDateError = false;

                                    }, 'Required Date', _requiredDateError),
                                    if (showDateError)
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: AppSizes.sm, left: AppSizes.md),
                                        child: Text(
                                          "Please Select Date",
                                          style: CustomTextStyles.b6.copyWith(
                                            color: Colors.redAccent,
                                          ),
                                        ),
                                      ),
                                    const SizedBox(
                                      height: AppSizes.defaultSpace,
                                    ),
                                    const TextFieldLabel(
                                      label: 'Remarks',
                                      isMandatory: true,
                                    ),
                                    CustomTextFormField(
                                      maxLines: 3,
                                      hintText: 'Write Note...',
                                      controller: remarksController,
                                    ),
                                    const SizedBox(
                                      height: AppSizes.imageThumbSize,
                                    ),
                                    Visibility(
                                      visible: _permissionService.hasPermission('radiology_centers', 'is_add'),
                                      child: CustomElevatedButton(
                                        text: 'Continue',
                                        onPressed: () {
                                          setState(() {
                                            // Triggering manual validation for fields
                                            showServicesError = doctorServiceController.selectedServices.isEmpty;
                                            showDateError = orderDate == null;
                                            showShadeError = selectedShade == null;
                                          });

                                          // if (_key.currentState!.validate() && !showDateError && !showServicesError && !showShadeError) {
                                          if (true) {
                                            List<Map<String, dynamic>> selectedServiceDetails = selectedServices.map((choice) {
                                              return {
                                                'id': choice.metadata?.id,  // Pass Service ID
                                                'name': choice.metadata?.servicename, // Pass Service Name
                                                'price': choice.metadata?.price, // Pass Service Price
                                                'quantity':1,
                                              };
                                            }).toList();

                                            // Map<String , dynamic> orderData = {
                                            //   'patient_name': patientNameController.text,
                                            //   'patient_id': patientIdController.text,
                                            //  'doctor_name' :doctorNameController.text,
                                            //   'hospital_name': hospitalNameController.text,
                                            //   'laboratory_name': labNameController.text,
                                            //   'selectedServices': selectedServices,
                                            //   'selectedServiceDetails': selectedServiceDetails,
                                            // };
                                            Map<String, dynamic> orderData = {
                                              'patientName': patientNameController.text,
                                              'patientId': patientIdController.text,
                                              'doctor_name': doctorNameController.text,
                                              'hospital_name': hospitalNameController.text,
                                              'laboratoryName': selectedLaboratory ?? "",
                                              'selectedServices': selectedServiceDetails,
                                              'selected_toOrgId': selectedRadiologySupplierId,
                                              'reasonForScan': reasonForScanController.text,
                                              'remarks': remarksController.text,
                                             // 'selectedServiceDetails': selectedServiceDetails,
                                              'orderDate': orderDate != null ? DateFormat('yyyy-MM-dd').format(orderDate!) : '',
                                              'requiredDate': requiredDate != null ? DateFormat('yyyy-MM-dd').format(requiredDate!) : '',
                                            };
                                            Get.to(() => const DentalPlaceOrderScreen(serviceName: "Radiology Centers",),
                                                arguments: orderData);
                                            // All validations passed, proceed to checkout
                                            log("Form is valid. Proceeding to checkout...");
                                          } else {
                                            // Some fields are invalid
                                            log("Form validation failed. Check required fields.");
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                    ),
                  ),
                ),
              ),
            );
          }
        ),
      ),
    );
  }

}
