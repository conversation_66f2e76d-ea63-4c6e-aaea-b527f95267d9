import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/delivery_boy/delivery_create_order_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_doctor_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_service_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/deliveryboy/delivery_create_doctor_screen.dart';
import 'package:platix/view/screens/deliveryboy/delivery_order_checkout_screen.dart';
import 'package:platix/view/widgets/multi_select_dropdown/core/multi_select.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';

import '../../../api/data_store.dart';
import '../../../controllers/signin_controller.dart';
import '../../../data/models/owner/owner_doctor_model.dart';
import '../../../data/models/user_model.dart';



class DeliveryCreateorderScreen extends StatefulWidget {
  const DeliveryCreateorderScreen({super.key});

  @override
  State<DeliveryCreateorderScreen> createState() => _DeliveryCreateorderScreenState();
}

class _DeliveryCreateorderScreenState extends State<DeliveryCreateorderScreen> {
  bool _orderDateError = false;
  bool _requiredDateError = false;
  final DeliveryDoctorController deliveryDoctorController = Get.find();
  final DeliveryServiceController deliveryServiceController = Get.find();

  final DeliveryCreateOrderController createOrderController = Get.put(DeliveryCreateOrderController());
  List<DoctorResult> filteredList= [];
  DoctorResult? selectedDoctor;
  final userRecord = UserRecord.fromJson(getData.read('userRecord'));

  final TextEditingController doctorNameController = TextEditingController();
  final TextEditingController hospitalNameController = TextEditingController();
  late TextEditingController labNameController = TextEditingController();
  final TextEditingController toothNameController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController patientIdController = TextEditingController();

  final SignInController signInController = Get.put(SignInController());

  String? selectedvalue;
  String? selectedShade;
  List<String> items = const ['Light', 'Medium', 'Dark'];

  String searchQuery = "";
  String? selectedToothValue;
  String? gender;
  String? selectedImpressionType;
  String? selectedShadeType;
  String? toTooth;
  String? fromTooth;
  List<File> _files = [];

  bool _showServicesError = false;
  bool _showShadeError = false;
  bool _genderError = false;
  bool _toothError = false;
  bool _shadeGuideError = false;
  bool _impressionTypeError = false;
  bool _fileError = false;
  bool _doctorError = false;

  final GlobalKey<FormState> _key = GlobalKey<FormState>();
  DateTime? orderDate;
  DateTime? requiredDate;

  Future<void> _selectDate(BuildContext context, Function(DateTime) onDateSelected, {DateTime? firstDate, bool onlyToday = false}) async {
    final DateTime today = DateTime.now();
    final DateTime initialDate = firstDate ?? today;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate ?? today,
      lastDate: onlyToday ? today : DateTime(2100),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: AppColors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.white,
                backgroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  Future<void> _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      if (_files.length + result.files.length > 5) {
        Get.snackbar(
          'Limit Exceeded',
          'You can only upload a maximum of 5 files.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      for (var pickedFile in result.files) {
        if (_files.any((f) => f.path.split('/').last == pickedFile.name)) {
          Get.snackbar(
            'File Already Selected',
            '${pickedFile.name} has already been selected.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          continue;
        }
        final file = File(pickedFile.path!);
        final extension = file.path.split('.').last.toLowerCase();
        if (extension != 'stl') {
          Get.snackbar(
            'Invalid File Type',
            '${pickedFile.name} is not a valid STL file.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          continue;
        }

        if (file.lengthSync() > 10 * 1024 * 1024) {
          Get.snackbar(
            'File Too Large',
            '${pickedFile.name} exceeds the 10 MB size limit.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        } else {
          setState(() {
            _files.add(file);
          });
        }
      }
    } else {
      log('User canceled the picker');
    }
  }

  @override
  void dispose() {
    doctorNameController.dispose();
    hospitalNameController.dispose();
    labNameController.dispose();
    toothNameController.dispose();
    ageController.dispose();
    remarksController.dispose();
    patientIdController.dispose();
    patientNameController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    orderDate = DateTime.now();
    labNameController = TextEditingController(text: userRecord.organization?.name);
    // if (userRecord.organization!.organizationService!.isNotEmpty) {
    //   log("Service list initialized with ${userRecord.organization!.organizationService!.length} items");
    // } else {
    //   log("Service list is empty");
    // }
    final services = userRecord.organization?.organizationService;
    if (services != null && services.isNotEmpty) {
      log("Service list initialized with ${services.length} items");
    } else {
      log("Service list is empty");
    }

    createOrderController.showNoResultsMessage.value = false;
    setState(() {
      signInController.selectedServices.clear();

    });
  }


  Future<void> performSearch(String query) async {


    if (query.trim().isEmpty) {
      setState(() {
        filteredList = [];
        createOrderController.showNoResultsMessage.value = false;
      });
      return;
    }

    List<DoctorResult> searchResult = await createOrderController.searchDoctor(query);
    setState(() {
      filteredList = searchResult;
      createOrderController.showNoResultsMessage.value = query.isNotEmpty && searchResult.isEmpty;
    });


    log("🔍 Search Results: ${filteredList.length} doctors found.");
  }



  @override
  Widget build(BuildContext context) {
    print("hhhhhhhhhhhhhhhhhhhhhh");
    print(userRecord.organization?.organizationType?.organizationType);
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Create Order',
        textColor: Colors.white,
        backgroundColor: AppColors.primary,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: FocusScope.of(context).unfocus,
          child: Center(
            child: SingleChildScrollView(
              child: Form(
                key: _key,
                child: Obx(() {
                  return Container(
                    color: Colors.white,
                    //width: MediaQuery.of(context).size.width > 600 ? Get.size.width * 0.5 : double.infinity,
                    width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: AppSizes.defaultSpace,
                        ),
                        Center(
                          child: Container(
                            margin:
                            const EdgeInsets.symmetric(horizontal: AppSizes.md),
                            // height: 48,
                            decoration: BoxDecoration(
                              color: AppColors.background1,
                              borderRadius: BorderRadius.circular(
                                  AppSizes.borderRadiusSm), // Optional
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primary.withOpacity(0.1),
                                  offset: const Offset(0, 1),
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                            child: CustomSearchField<DoctorResult>(
                              fetchItems: (String query) async {
                                if (query.trim().isEmpty || query.length < 2) {
                                  return []; // Or return cached suggestions or nothing
                                }
                                return await createOrderController.searchDoctor(query);
                              },
                              hintText:
                              "Search for Doctors, Hospitals, Phone numbers...",
                                itemAsString: (doctor) =>
                                "${doctor.prefix} ${doctor.firstName} ${doctor.lastName} (${doctor.organization?.name})",
                                onSelected: (doctor) {
                                  if (doctor != null) {
                                    doctorNameController.text =
                                    "${doctor.prefix} ${doctor.firstName} ${doctor.lastName}";
                                    hospitalNameController.text = doctor.organization?.name ?? '';
                                    selectedDoctor = doctor;
                                    createOrderController.showNoResultsMessage.value = false;
                                    setState(() {
                                      _doctorError = false;
                                    });
                                  }
                                },
                              onChanged: (query) {
                                log("Search Query: $query");
                                performSearch(query);
                              },
                             // defaultItems: deliveryDoctorController.searchResult,
                            ),
                          ),
                        ),
                        if (_doctorError)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0, left: AppSizes.md),
                            child: Text(
                              "Please select a doctor",
                              style: CustomTextStyles.b6.copyWith(color: Colors.red),
                            ),
                          ),
                        const SizedBox(
                          height: kIsWeb ? AppSizes.spaceBtwItems : 0,
                        ),
                        Padding(
                          padding:const EdgeInsets.all(AppSizes.md),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (createOrderController.showNoResultsMessage.value)
                                Container(
                                  height: kIsWeb ? 130 :110,
                                  width: 380,
                                  margin:
                                  kIsWeb ? const EdgeInsets.symmetric(horizontal: 75): const EdgeInsets.all(0),
                                  child: Center(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Text(
                                          'Doctor not found !',
                                          style: CustomTextStyles.b2
                                              .copyWith(color: AppColors.primary),
                                        ),
                                        const SizedBox(
                                          height: AppSizes.xs,
                                        ),
                                        Text(
                                          'Want to create new doctor',
                                          style: CustomTextStyles.b2
                                              .copyWith(color: AppColors.primary),
                                        ),
                                        const SizedBox(
                                          height: AppSizes.spaceBtwItems,
                                        ),
                                        CustomElevatedButton(
                                          text: 'Create Doctor',
                                          height: AppSizes.iconLg,
                                          width: kIsWeb ? 220 :160,
                                          onPressed: () {
                                            Get.to(() => const DeliveryCreatedoctorScreen());
                                          },
                                          buttonStyle: ButtonStyle(
                                            shape: WidgetStatePropertyAll(
                                                RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(
                                                        AppSizes.borderRadiusSm))),
                                            padding: const WidgetStatePropertyAll(
                                              EdgeInsets.only(right: 10),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(
                                          height: kIsWeb ? AppSizes.sm2:AppSizes.sm2,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),



                              LabelTextField(label: 'Name Of The Doctor', hint: 'Enter Doctor Name', controller: doctorNameController, validator: AppValidators.validateText,isDisabled: true,),
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              LabelTextField(label: 'Name of Hospital', hint: 'Enter Hospital Name', controller: hospitalNameController, validator: AppValidators.validateText, isDisabled: true,),
                              const SizedBox(height: AppSizes.defaultSpace),

                              if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory') ...[
                                LabelTextField(
                                  label: 'Name Of The Patient',
                                  hint: 'Enter Patient Name',
                                  controller: patientNameController,
                                  validator: AppValidators.validateText,
                                  isMandatory: false,
                                ),
                                const SizedBox(height: AppSizes.defaultSpace),
                                LabelTextField(
                                  label: 'Patient Id',
                                  hint: 'Enter Patient Id',
                                  isMandatory: false,
                                  controller: patientIdController,
                                  validator: AppValidators.validateText,
                                ),
                                const SizedBox(
                                  height: AppSizes.defaultSpace,
                                ),
                                LabelTextField(
                                  label: 'Age',
                                  hint: 'Enter Age',
                                  controller: ageController,
                                  inputType: TextInputType.number,
                                  validator: AppValidators.validateText,
                                  isMandatory: false,
                                ),
                                const SizedBox(height: AppSizes.defaultSpace),
                                const TextFieldLabel(label: 'Gender'),
                                Row(
                                  children: [
                                    Radio<String>(
                                      value: 'Male',
                                      groupValue: gender,
                                      onChanged: (value) {
                                        setState(() {
                                          gender = value;
                                          _genderError = false;
                                        });
                                      },
                                    ),
                                    const Text('Male'),
                                    Radio<String>(
                                      value: 'Female',
                                      groupValue: gender,
                                      onChanged: (value) {
                                        setState(() {
                                          gender = value;
                                          _genderError = false;
                                        });
                                      },
                                    ),
                                    const Text('Female'),
                                  ],
                                ),
                                if (_genderError)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      "Gender is required",
                                      style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                    ),
                                  ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],
                              //

                             // if(createOrderController.detailsModel?.data.toOrganizationDetails.name=="Material Supplier")
                              LabelTextField(
                                  label: getOrgLabel(userRecord.organization?.organizationType?.organizationType),
                                  hint: 'Enter Material Name',
                                  controller: labNameController,
                                  validator: AppValidators.validateText,
                                isDisabled: true,
                              ),
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              //const TextFieldLabel(label: 'Service Name'),
                              // Padding(
                              //   padding: const EdgeInsets.all(0),
                              //   child: CustomMultiSelectDropdown(
                              //     hintText: 'Select Services',
                              //     items: deliveryServiceController.serviceList
                              //         .expand((serviceData) => serviceData.services)
                              //         .map<String>((service) => service.name)
                              //         .toList(),
                              //     selectedValues: selectedServices,
                              //     onChanged: (selectedItems) {
                              //       selectedServices = selectedItems;
                              //       showServicesError = false;
                              //     },
                              //   ),
                              // ),

                              // CustomSearchDropdown1(
                              //   hintText: 'Select Services',
                              //   items: deliveryServiceController.services
                              //       .map((service) => service.toJson())
                              //       .toList(),
                              //   onItemSelected: (selectedItem) {
                              //     final selectedItem1 = DeliveryService1.fromJson(selectedItem);
                              //     selectedServices.add(selectedItem1);
                              //     showServicesError = false;
                              //   },
                              //   selectType: SelectionType.multiSelect,
                              //   selectedItems: selectedServices
                              //       .map((service) => service.toJson())
                              //       .toList(), // Convert selected services to Map<String, dynamic>
                              // ),
                              TextFieldLabel(label: userRecord.type == 'Material Supplier' ? 'Materials' : 'Service Name'),
                              MultiSelectField<OrganizationService>(
                                label: "Select Services",
                                textStyleLabel: CustomTextStyles.b4_1.copyWith(color: AppColors.darkGrey),
                                //   data: ()=> deliveryServiceController.services.map<Choice<DeliveryService1>>((service) => Choice<DeliveryService1>(service.id.toString(), service.name, metadata: service)).toList(),
                                //   onSelect: (List<Choice<DeliveryService1>> selectedChoices, bool isFromDefaultData){
                                //     deliveryServiceController.selectedServices.assignAll(selectedChoices);
                                //     showServicesError = falsef;
                                //   },
                                // defaultData: deliveryServiceController.selectedServices,
                                data:(){
                                  final services = userRecord.organization?.organizationService;

                                  log("Dropdown services: ${services?.length}");
                                  return services?.map<Choice<OrganizationService>>((service) => Choice<OrganizationService>(
                                    service.id.toString(),
                                    service.servicess?.servicename ?? '',
                                    metadata: service,
                                  )).toList() ?? [];
                                },
                                onSelect: (List<Choice<OrganizationService?>> selectedChoices, bool isFromDefaultData){
                                  signInController.selectedServices.assignAll(selectedChoices.cast<Choice<OrganizationService>>());
                                  _showServicesError = false;
                                },
                                defaultData: signInController.selectedServices,

                                cleanCurrentSelection: deliveryServiceController.cleanCurrentSelection.value,
                                singleSelection: true,
                                useTextFilter: true,
                                menuStyle: MenuStyle(
                                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8))),
                                  backgroundColor: const WidgetStatePropertyAll(Colors.white),
                                  maximumSize:  WidgetStatePropertyAll<Size>(
                                    Size(Get.size.width, 200), // Set width and height manually
                                  ),
                                ),
                                itemColor: ItemColor(
                                    selected: Colors.purple.withOpacity(0.2),
                                    unSelected: Colors.white
                                ),
                                itemMenuStyle: CustomTextStyles.b4_1,
                                isDisabled: false,
                              ),

                              if (_showServicesError)
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: AppSizes.sm, left: AppSizes.md),
                                  child: Text(
                                    "Please Select Atleast One Service",
                                    style: CustomTextStyles.b6.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              // const SizedBox(
                              //   height: AppSizes.defaultSpace,
                              // ),
                              if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory') ...[
                                const SizedBox(height: AppSizes.defaultSpace),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const TextFieldLabel(label: 'Select Tooth'),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: CustomDropdown(
                                            hintText: 'From',
                                            items: List.generate(38, (index) => (index + 11).toString()),
                                            selectedValue: fromTooth,
                                            onChanged: (value) {
                                              setState(() {
                                                fromTooth = value;
                                                _toothError = false;
                                              });
                                            },
                                          ),
                                        ),
                                        const SizedBox(width: AppSizes.spaceBtwItems),
                                        Expanded(
                                          child: CustomDropdown(
                                            hintText: 'To',
                                            items: List.generate(38, (index) => (index + 11).toString()),
                                            selectedValue: toTooth,
                                            onChanged: (value) {
                                              setState(() {
                                                toTooth = value;
                                                _toothError = false;
                                              });
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (_toothError)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 8.0),
                                        child: Text(
                                          "Please select both 'From' and 'To' tooth",
                                          style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                        ),
                                      ),
                                  ],
                                ),

                              ],
                              const SizedBox(height: AppSizes.defaultSpace),

                              if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory') ...[
                                const TextFieldLabel(label: 'Shades'),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Vita shade guide',
                                        groupValue: selectedShadeType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeType = value;
                                            selectedShade = null;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita shade guide'),
                                      Radio<String>(
                                        value: 'Vita 3D master shade',
                                        groupValue: selectedShadeType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeType = value;
                                            selectedShade = null;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita 3D master shade'),
                                    ],
                                  ),
                                ),
                                if (_shadeGuideError)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      "Please select a shade guide",
                                      style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                    ),
                                  ),
                                if (selectedShadeType != null)
                                  CustomDropdown(
                                    hintText: 'Select Shade',
                                    items: selectedShadeType == 'Vita shade guide'
                                        ? ['A1 (Reddishbrown)', 'A2', 'A3', 'A4', 'B1 (Reddish yellow)', 'B2', 'B3', 'B4', 'C1 (Grey)', 'C2', 'C3', 'C4 (Reddish grey)', 'D2', 'D3', 'D4']
                                        : ['1M-1', '1M-2', '2M-1', '2M-2', '2M-3', '2L-1.5', '2L-2.5', '2R-1.5', '2R-2.5', '3L-1.5', '3L-2.5', '3M-1', '3M-2', '3M-3', '3R-1.5', '3R-2.5', '4L-1.5', '4L-2.5', '4M-1', '4M-2', '4M-3', '4R-1.5', '4R-2.5', '5M-1', '5M-2', '5M-3'],
                                    selectedValue: selectedShade,
                                    onChanged: (value) {
                                      setState(() {
                                        selectedShade = value;
                                        _showShadeError = false;
                                      });
                                    },
                                  ),
                                if (_showShadeError)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      "Shade is required",
                                      style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                    ),
                                  ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],

                              if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory') ...[
                                const TextFieldLabel(label: 'Type of Impression'),
                                Row(
                                  children: [
                                    Radio<String>(
                                      value: 'Physical',
                                      groupValue: selectedImpressionType,
                                      onChanged: (value) {
                                        setState(() {
                                          selectedImpressionType = value;
                                          _impressionTypeError = false;
                                        });
                                      },
                                    ),
                                    const Text('Physical'),
                                    Radio<String>(
                                      value: 'Digital',
                                      groupValue: selectedImpressionType,
                                      onChanged: (value) {
                                        setState(() {
                                          selectedImpressionType = value;
                                          _impressionTypeError = false;
                                        });
                                      },
                                    ),
                                    const Text('Digital'),
                                  ],
                                ),
                                if (_impressionTypeError)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      "Please select the type of impression",
                                      style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                    ),
                                  ),
                                if (selectedImpressionType == 'Digital')
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CustomTextFormField(
                                        hintText: _files.isEmpty ? 'No file chosen' : '${_files.length} file(s) selected',
                                        suffix: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                                          child: CustomElevatedButton(
                                            height: 34,
                                            width: 100,
                                            text: 'Choose',
                                            onPressed: _pickFiles,
                                            buttonStyle: ButtonStyle(shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)), padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))),
                                          ),
                                        ),
                                        validator: (value) {
                                          if (_files.isEmpty) {
                                            return 'Please upload a file';
                                          }
                                          for (var file in _files) {
                                            if (file.path.split('.').last.toLowerCase() != 'stl') {
                                              return 'Please upload a valid STL file';
                                            }
                                            if (file.lengthSync() > 10 * 1024 * 1024) {
                                              return 'File size should not exceed 10 MB';
                                            }
                                          }
                                          return null;
                                        },
                                      ),
                                      if (_fileError)
                                        Padding(
                                          padding: const EdgeInsets.only(top: 8.0),
                                          child: Text(
                                            "Please upload at least one STL file",
                                            style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                          ),
                                        ),
                                      const SizedBox(height: AppSizes.spaceSmall),
                                      ..._files.map((file) => Row(
                                        children: [
                                          Expanded(child: Text(file.path.split('/').last)),
                                          IconButton(
                                            icon: const Icon(Icons.remove_circle),
                                            onPressed: () {
                                              setState(() {
                                                _files.remove(file);
                                              });
                                            },
                                          ),
                                        ],
                                      )),
                                      const SizedBox(height: AppSizes.spaceSmall),
                                      const Text('(Upload up to 5 STL digital impression files)'),
                                    ],
                                  ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],

                              //const TextFieldLabel(label: 'Order Date'),
                              _buildDateField(orderDate, (date) {
                                setState(() {
                                  orderDate = date;
                                  if (requiredDate != null && requiredDate!.isBefore(orderDate!)) {
                                    requiredDate = null;
                                  }
                                  _orderDateError = false;
                                });
                              }, 'Order Date', _orderDateError),
                              if (_orderDateError)
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: AppSizes.sm, left: AppSizes.md),
                                  child: Text(
                                    "Please Select Date",
                                    style: CustomTextStyles.b6.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              _buildDateField(requiredDate, (date) {
                                setState(() {
                                  requiredDate = date;
                                  _requiredDateError = false;
                                });
                              }, 'Required Date', _requiredDateError, firstDate: orderDate),
                              if (_requiredDateError)
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: AppSizes.sm, left: AppSizes.md),
                                  child: Text(
                                    "Please Select Date",
                                    style: CustomTextStyles.b6.copyWith(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              const TextFieldLabel(
                                label: 'Remarks',
                                isMandatory: false,
                              ),
                              CustomTextFormField(
                                maxLines: 3,
                                hintText: 'Write Note...',
                                controller: remarksController,
                              ),
                              const SizedBox(
                                height: AppSizes.defaultSpace,
                              ),
                              // CustomElevatedButton(
                              //   text: 'Continue',
                              //   onPressed: () {
                              //     setState(() {
                              //       // Triggering manual validation for fields
                              //       showServicesError = signInController.selectedServices.isEmpty;
                              //       showDateError = orderDate == null;
                              //       showShadeError = selectedShade == null;
                              //     });
                              //
                              //     // if (_key.currentState!.validate() && !showDateError && !showServicesError && !showShadeError) {
                              //     if (true) {
                              //       List<Map<String, dynamic>>
                              //       selectedServiceDetails = signInController.selectedServices.map((serviceName) {
                              //         final service = userRecord.organization?.organizationService
                              //             ?.where((service) => service.id == serviceName.metadata?.id)
                              //             .toList()
                              //             .firstOrNull;
                              //         log("${serviceName.metadata?.id}");
                              //         log('${service?.id}');
                              //         log('${service?.servicess?.servicename}');
                              //         log('${service?.price}');
                              //         return {
                              //           'id' :service?.id,
                              //           'name': service?.servicess?.servicename ?? '',
                              //           'price': service?.price
                              //         };
                              //       }).toList();
                              //
                              //       // Map<String , dynamic> orderData = {
                              //       //   'patient_name': patientNameController.text,
                              //       //   'patient_id': patientIdController.text,
                              //       //  'doctor_name' :doctorNameController.text,
                              //       //   'hospital_name': hospitalNameController.text,
                              //       //   'laboratory_name': labNameController.text,
                              //       //   'selectedServices': selectedServices,
                              //       //   'selectedServiceDetails': selectedServiceDetails,
                              //       // };
                              //       Map<String, dynamic> orderData = {
                              //         'patient_name': patientNameController.text,
                              //         'patient_id': patientIdController.text,
                              //         'doctor_name': doctorNameController.text,
                              //         'hospital_name': hospitalNameController.text,
                              //         'remark': remarksController.text,
                              //         'orderDate' : orderDate != null
                              //             ? DateFormat('yyyy-MM-dd').format(orderDate!)
                              //             : '',
                              //         'requiredDate' : requiredDate != null
                              //             ? DateFormat('yyyy-MM-dd').format(requiredDate!)
                              //             : '',
                              //         'laboratory_name': labNameController.text,
                              //         'tooth_name':selectedToothValue,
                              //         'shade':selectedShade,
                              //
                              //         //'selectedServices': selectedServiceDetails,
                              //         'selectedServiceDetails':
                              //         selectedServiceDetails,
                              //       };
                              //       Get.to(() =>  DeliveryCheckoutScreen(orgName: userRecord.organization?.organizationType?.organizationType),
                              //           arguments: {'order_data' : orderData, 'selected_doctor': selectedDoctor});
                              //       // All validations passed, proceed to checkout
                              //       log("Form is valid. Proceeding to checkout...");
                              //     } else {
                              //       // Some fields are invalid
                              //       log("Form validation failed. Check required fields.");
                              //     }
                              //   },
                              // ),

                              CustomElevatedButton(
                                text: 'Continue',
                                onPressed: () {
                                  setState(() {
                                    _doctorError = selectedDoctor == null;
                                    _showServicesError = signInController.selectedServices.isEmpty;
                                    _orderDateError = orderDate == null;
                                    _requiredDateError = requiredDate == null;
                                    if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory') {
                                      _genderError = gender == null;
                                      _toothError = fromTooth == null || toTooth == null;
                                      _shadeGuideError = selectedShadeType == null;
                                      _showShadeError = (selectedShadeType == 'Vita shade guide' && selectedShade == null) ||
                                          (selectedShadeType == 'Vita 3D master shade' && selectedShade == null);
                                      _impressionTypeError = selectedImpressionType == null;
                                      _fileError = selectedImpressionType == 'Digital' && _files.isEmpty;
                                    }
                                  });


                                  if (_key.currentState!.validate() &&
                                      !_doctorError &&
                                      !_showServicesError &&
                                      !_orderDateError &&
                                      !_requiredDateError &&
                                      (!_genderError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory') &&
                                      (!_toothError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory') &&
                                      (!_shadeGuideError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory') &&
                                      (!_showShadeError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory') &&
                                      (!_impressionTypeError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory') &&
                                      (!_fileError || userRecord.organization?.organizationType?.organizationType != 'Dental Laboratory')) {
                                    List<Map<String, dynamic>> selectedServiceDetails = signInController.selectedServices.map((serviceName) {
                                      final service = userRecord.organization?.organizationService
                                          ?.where((service) => service.id == serviceName.metadata?.id)
                                          .toList()
                                          .firstOrNull;
                                      log("${serviceName.metadata?.id}");
                                      log('${service?.id}');
                                      log('${service?.servicess?.servicename}');
                                      log('${service?.price}');
                                      return {
                                        'id' : service?.id,
                                        'name': service?.servicess?.servicename ?? '',
                                        'price': service?.price
                                      };
                                    }).toList();

                                    Map<String, dynamic> orderData = {
                                      'patient_name': patientNameController.text,
                                      'patient_id': patientIdController.text,
                                      'doctor_name': doctorNameController.text,
                                      'hospital_name': hospitalNameController.text,
                                      'remark': remarksController.text,
                                      'orderDate': orderDate != null ? DateFormat('yyyy-MM-dd').format(orderDate!) : '',
                                      'requiredDate': requiredDate != null ? DateFormat('yyyy-MM-dd').format(requiredDate!) : '',
                                      'laboratory_name': labNameController.text,
                                      'shade': selectedShade,
                                      'selectedServiceDetails': selectedServiceDetails,
                                      'toothMappings': fromTooth != null && toTooth != null ? [{'from_tooth': fromTooth, 'to_tooth': toTooth, 'tooth_order': 1}] : [],
                                      'stl_files': _files,
                                      'impression_type': selectedImpressionType ?? '',
                                      'shade_type': selectedShadeType ?? '',
                                      'age': ageController.text,
                                      'gender': gender ?? '',
                                    };

                                     log("Order Data delivery: $orderData");

                                    Get.to(() => DeliveryCheckoutScreen(
                                        orgName: userRecord.organization?.organizationType?.organizationType
                                    ),
                                        arguments: {
                                      'order_data': orderData,
                                      'selected_doctor': selectedDoctor
                                    });

                                    log("Form is valid. Proceeding to checkout...");
                                  } else {
                                    log("Form validation failed. Check required fields.");
                                  }
                                },
                              ),

                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected, String label, bool showError, {DateTime? firstDate}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: () => _selectDate(context, onDateSelected, firstDate: firstDate),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null
                      ? DateFormat('dd-MM-yyyy').format(date)
                      : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(
                      color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2
                      .copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: CustomTextStyles.b6.copyWith(color: Colors.red),
            ),
          ),
      ],
    );
  }

  String getOrgLabel(String? orgName) {
   // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supply";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}
