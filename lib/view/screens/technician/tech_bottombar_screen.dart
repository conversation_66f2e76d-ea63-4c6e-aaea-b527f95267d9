import 'package:flutter/material.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/icon_constants.dart';
import '../../../utils/constants/sizes.dart';
import '../../widgets/custom_image_view.dart';
import 'tech_home_screen.dart';
import 'tech_orderlist_screen.dart';
import 'tech_profile_screen.dart';

class TechBottombarScreen extends StatefulWidget {
  final int initialIndex;
  final int? initialOrderTab;

  const TechBottombarScreen({
    super.key,
    this.initialIndex = 0,
    this.initialOrderTab,
  });

  @override
  State<TechBottombarScreen> createState() => _TechBottombarScreenState();
}

class _TechBottombarScreenState extends State<TechBottombarScreen> {
  late int _selectedIndex;
  late int? _initialOrderTab;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
    _initialOrderTab = widget.initialOrderTab;
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      if (index == 1) _initialOrderTab = null; // Clear after first use
    });
  }

  Widget _getScreen(int index) {
    switch (index) {
      case 0:
        return const TechHomeScreen();
      case 1:
        return TechOrderlistScreen(initialTab: _initialOrderTab ?? 0);
      case 2:
        return const TechProfileScreen();
      default:
        return const TechHomeScreen();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: _getScreen(_selectedIndex),
      bottomNavigationBar: SizedBox(
        height: 90,
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppSizes.borderRadiusLg),
              topRight: Radius.circular(AppSizes.borderRadiusLg),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.16),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppSizes.borderRadiusLg),
              topRight: Radius.circular(AppSizes.borderRadiusLg),
            ),
            child: BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              elevation: AppSizes.elevationLevel2, // M3 elevation
              backgroundColor: Theme.of(context).colorScheme.surface, // M3 surface color
              items: [
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.homeOutlined,
                    width: AppSizes.iconMd, // M3 icon size
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.home,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Home',
                ),
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.ordersOutlined,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.orders,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Orders',
                ),
                BottomNavigationBarItem(
                  icon: CustomImageView(
                    imagePath: AppIcons.profileOutlined,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  activeIcon: CustomImageView(
                    imagePath: AppIcons.profile,
                    width: AppSizes.iconMd,
                    height: AppSizes.iconMd,
                  ),
                  label: 'Profile',
                ),
              ],
              currentIndex: _selectedIndex,
              selectedItemColor: Theme.of(context).colorScheme.primary, // M3 primary color
              unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6), // M3 unselected color
              selectedLabelStyle: Theme.of(context).textTheme.labelMedium?.copyWith( // M3 typography
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: Theme.of(context).textTheme.labelMedium, // M3 typography
              onTap: _onItemTapped,
            ),
          ),
        ),
      ),
    );
  }
}
