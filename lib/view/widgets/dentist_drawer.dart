import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/appointments_screen.dart';
import 'package:platix/view/screens/dentist/billing_screen.dart';
import 'package:platix/view/screens/dentist/dashboard_screen.dart';
import 'package:platix/view/screens/dentist/edr_screen.dart';
import 'package:platix/view/screens/dentist/reminders_screen.dart';
import 'package:platix/view/screens/dentist/reports_screen.dart';
import 'package:platix/view/screens/dentist/dentist_homescreen.dart';
import 'package:platix/view/screens/dentist/dentist_bottombar.dart'; // Import DentistBottomBar

class DentistDrawer extends StatefulWidget {
  const DentistDrawer({super.key});

  @override
  State<DentistDrawer> createState() => _DentistDrawerState();
}

class _DentistDrawerState extends State<DentistDrawer> {
  String selectedItem = 'Home';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Drawer(
      backgroundColor: theme.colorScheme.surface, // M3 surface color
      surfaceTintColor: theme.colorScheme.surfaceTint, // M3 surface tint
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(color: theme.colorScheme.primary), // M3 primary color
            child: CustomImageView(
              fit: BoxFit.none,
              imagePath: AppIcons.appLogo,
              color: theme.colorScheme.onPrimary, // M3 on-primary color
            ),
          ),
          _buildDrawerItem(
            icon: Icons.home,
            title: 'Home',
            onTap: () {
              Get.to(() => const DentistHomeScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.dashboard,
            title: 'Dashboard',
            onTap: () {
              Get.to(() => const DashboardScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.calendar_today,
            title: 'Appointments',
            onTap: () {
              Get.to(() => const AppointmentsScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.person_add,
            title: 'Registration',
            onTap: () {
              Get.toNamed(AppRoutes.patientRegistrationScreen);
            },
          ),
          _buildDrawerItem(
            icon: Icons.receipt,
            title: 'Billing',
            onTap: () {
              Get.to(() => const BillingScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.description,
            title: 'EDR',
            onTap: () {
              Get.to(() => const EdrScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.biotech,
            title: 'Lab Records',
            onTap: () {
              Get.to(() => const DentistBottomBar(
                  initialIndex: 1)); // Navigate to DentistBottomBar with Orders tab selected
            },
          ),
          _buildDrawerItem(
            icon: Icons.image,
            title: 'Imaging Orders',
            onTap: () {
              Get.to(() => const DentistBottomBar(
                  initialIndex: 1)); // Navigate to DentistBottomBar with Orders tab selected
            },
          ),
          _buildDrawerItem(
            icon: Icons.notifications,
            title: 'Reminders',
            onTap: () {
              Get.to(() => const RemindersScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.bar_chart,
            title: 'Reports',
            onTap: () {
              Get.to(() => const ReportsScreen());
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final isSelected = selectedItem == title;
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withValues(alpha: 0.6), // M3 colors
        size: AppSizes.iconMd, // M3 icon size
      ),
      title: Text(
        title,
        style: theme.textTheme.bodyLarge?.copyWith( // M3 typography
          color: isSelected
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      onTap: () {
        setState(() {
          selectedItem = title;
        });
        onTap();
      },
      selected: isSelected,
      selectedTileColor: theme.colorScheme.primary.withValues(alpha: 0.1), // M3 selected color
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusSm), // M3 border radius
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: AppSizes.md, // Reverted to original spacing
        vertical: AppSizes.sm,
      ),
    );
  }
}
