import 'package:flutter/material.dart';
import '../../../utils/app_export.dart';
import '../../../utils/accessibility_utils.dart';

/// Standardized M3 Loading State Components
/// Provides consistent loading indicators and error states across the app
class M3LoadingStates {
  M3LoadingStates._();

  /// Standard loading indicator with M3 design
  static Widget loadingIndicator({
    String? message,
    double size = 36,
    Color? color,
    bool showMessage = true,
  }) {
    return AccessibilityUtils.accessibleLoadingIndicator(
      label: message ?? 'Loading',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              color: color ?? AppColors.primary,
              strokeWidth: 3,
            ),
          ),
          if (showMessage && message != null) ...[
            SizedBox(height: AppSizes.sm2), // Reverted to original spacing
            Text(
              message,
              style: CustomTextStyles.b3.copyWith(
                color: AppColors.darkerGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// Full screen loading overlay
  static Widget fullScreenLoading({
    String message = 'Loading...',
    Color? backgroundColor,
  }) {
    return Container(
      color: backgroundColor ?? Colors.black.withValues(alpha: 0.3),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: loadingIndicator(
            message: message,
            size: 48,
          ),
        ),
      ),
    );
  }

  /// Inline loading state for buttons
  static Widget buttonLoading({
    Color? color,
    double size = 20,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        color: color ?? Colors.white,
        strokeWidth: 2,
      ),
    );
  }

  /// Loading state for list items
  static Widget listItemLoading({
    double height = 80,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      height: height,
      margin: margin ?? EdgeInsets.symmetric(
        horizontal: AppSizes.md, // Reverted to original spacing
        vertical: AppSizes.spaceSmall,
      ),
      decoration: BoxDecoration(
        color: AppColors.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const M3ShimmerEffect(),
    );
  }

  /// Error state widget
  static Widget errorState({
    required String title,
    required String message,
    String? buttonText,
    VoidCallback? onRetry,
    IconData? icon,
    Color? iconColor,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: 64,
              color: iconColor ?? AppColors.red,
            ),
            SizedBox(height: AppSizes.md), // Reverted to original spacing
            Text(
              title,
              style: CustomTextStyles.h5.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppSizes.spaceSmall),
            Text(
              message,
              style: CustomTextStyles.b3.copyWith(
                color: AppColors.darkerGrey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null && buttonText != null) ...[
              SizedBox(height: AppSizes.lg), // Reverted to original spacing
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(buttonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Empty state widget
  static Widget emptyState({
    required String title,
    required String message,
    String? buttonText,
    VoidCallback? onAction,
    IconData? icon,
    Color? iconColor,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: 64,
              color: iconColor ?? AppColors.grey,
            ),
            SizedBox(height: AppSizes.md), // Reverted to original spacing
            Text(
              title,
              style: CustomTextStyles.h5.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppSizes.spaceSmall),
            Text(
              message,
              style: CustomTextStyles.b3.copyWith(
                color: AppColors.darkerGrey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            if (onAction != null && buttonText != null) ...[
              SizedBox(height: AppSizes.lg), // Reverted to original spacing
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add),
                label: Text(buttonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Network error state
  static Widget networkError({
    VoidCallback? onRetry,
  }) {
    return errorState(
      title: 'Connection Error',
      message: 'Please check your internet connection and try again.',
      buttonText: 'Retry',
      onRetry: onRetry,
      icon: Icons.wifi_off,
      iconColor: AppColors.orange,
    );
  }

  /// Generic error state
  static Widget genericError({
    String? message,
    VoidCallback? onRetry,
  }) {
    return errorState(
      title: 'Something went wrong',
      message: message ?? 'An unexpected error occurred. Please try again.',
      buttonText: 'Retry',
      onRetry: onRetry,
    );
  }
}

/// Shimmer effect for loading states
class M3ShimmerEffect extends StatefulWidget {
  final Widget? child;
  final Color? baseColor;
  final Color? highlightColor;

  const M3ShimmerEffect({
    super.key,
    this.child,
    this.baseColor,
    this.highlightColor,
  });

  @override
  State<M3ShimmerEffect> createState() => _M3ShimmerEffectState();
}

class _M3ShimmerEffectState extends State<M3ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseColor = widget.baseColor ?? AppColors.lightGrey.withValues(alpha: 0.3);
    final highlightColor = widget.highlightColor ?? AppColors.lightGrey.withValues(alpha: 0.1);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}
