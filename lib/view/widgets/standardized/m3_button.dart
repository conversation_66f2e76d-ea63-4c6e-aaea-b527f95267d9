import 'package:flutter/material.dart';
import '../../../utils/app_export.dart';
import '../../../theme/animations.dart';
import '../../../utils/accessibility_utils.dart';

/// Standardized M3 Button Component
/// Replaces CustomElevatedButton and CustomElevatedButton2 with consistent M3 design
class M3Button extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final M3ButtonType type;
  final M3ButtonSize size;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final bool isLoading;
  final bool isDisabled;
  final double? width;
  final EdgeInsetsGeometry? margin;
  final String? semanticLabel;

  const M3Button({
    super.key,
    required this.text,
    this.onPressed,
    this.type = M3ButtonType.primary,
    this.size = M3ButtonSize.medium,
    this.leftIcon,
    this.rightIcon,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.margin,
    this.semanticLabel,
  });

  // Factory constructors for common button types
  const M3Button.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = M3ButtonSize.medium,
    this.leftIcon,
    this.rightIcon,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.margin,
    this.semanticLabel,
  }) : type = M3ButtonType.primary;

  const M3Button.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.size = M3ButtonSize.medium,
    this.leftIcon,
    this.rightIcon,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.margin,
    this.semanticLabel,
  }) : type = M3ButtonType.secondary;

  const M3Button.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.size = M3ButtonSize.medium,
    this.leftIcon,
    this.rightIcon,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.margin,
    this.semanticLabel,
  }) : type = M3ButtonType.outlined;

  const M3Button.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = M3ButtonSize.medium,
    this.leftIcon,
    this.rightIcon,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.margin,
    this.semanticLabel,
  }) : type = M3ButtonType.text;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = !isDisabled && !isLoading && onPressed != null;

    Widget button = _buildButton(context, theme, isEnabled);

    if (margin != null) {
      button = Padding(padding: margin!, child: button);
    }

    return AccessibilityUtils.accessibleButton(
      child: button,
      onPressed: isEnabled ? onPressed : null,
      semanticLabel: semanticLabel ?? text,
      enabled: isEnabled,
      minTouchTarget: _getMinTouchTarget(),
    );
  }

  Widget _buildButton(BuildContext context, ThemeData theme, bool isEnabled) {
    final buttonStyle = _getButtonStyle(theme);
    final textStyle = _getTextStyle(theme);
    final height = _getHeight();

    Widget buttonChild = _buildButtonContent(textStyle);

    Widget button;
    switch (type) {
      case M3ButtonType.primary:
        button = ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case M3ButtonType.secondary:
        button = ElevatedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case M3ButtonType.outlined:
        button = OutlinedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case M3ButtonType.text:
        button = TextButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
    }

    return M3InteractiveButton(
      onPressed: isEnabled ? onPressed : null,
      child: SizedBox(
        height: height,
        width: width,
        child: button,
      ),
    );
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    if (isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == M3ButtonType.primary ? Colors.white : AppColors.primary,
          ),
        ),
      );
    }

    final children = <Widget>[];

    if (leftIcon != null) {
      children.add(leftIcon!);
      children.add(SizedBox(width: _getIconSpacing()));
    }

    children.add(
      Text(
        text,
        style: textStyle,
        textAlign: TextAlign.center,
      ),
    );

    if (rightIcon != null) {
      children.add(SizedBox(width: _getIconSpacing()));
      children.add(rightIcon!);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  ButtonStyle _getButtonStyle(ThemeData theme) {

    switch (type) {
      case M3ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          disabledBackgroundColor: AppColors.buttonDisabled,
          disabledForegroundColor: AppColors.grey,
          elevation: 2,
          shadowColor: AppColors.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          padding: _getPadding(),
        );
      case M3ButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.secondary,
          foregroundColor: Colors.white,
          disabledBackgroundColor: AppColors.buttonDisabled,
          disabledForegroundColor: AppColors.grey,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          padding: _getPadding(),
        );
      case M3ButtonType.outlined:
        return OutlinedButton.styleFrom(
          foregroundColor: AppColors.white,
          backgroundColor: AppColors.primary,
          disabledForegroundColor: AppColors.grey,
          side: BorderSide(color: AppColors.primary, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          padding: _getPadding(),
        );
      case M3ButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: AppColors.white,
          backgroundColor: AppColors.primary,
          disabledForegroundColor: AppColors.grey,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius()),
          ),
          padding: _getPadding(),
        );
    }
  }

  TextStyle _getTextStyle(ThemeData theme) {
    final baseStyle = size == M3ButtonSize.small
        ? CustomTextStyles.b6
        : size == M3ButtonSize.medium
            ? CustomTextStyles.b4
            : CustomTextStyles.b3;

    return baseStyle.copyWith(
      fontWeight: FontWeight.w600,
      letterSpacing: 0.1,
      color: Colors.white, // Explicitly set white text color for all button types
    );
  }

  double _getHeight() {
    switch (size) {
      case M3ButtonSize.small:
        return 36;
      case M3ButtonSize.medium:
        return AppSizes.recommendedTouchTarget;
      case M3ButtonSize.large:
        return AppSizes.largeTouchTarget;
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case M3ButtonSize.small:
        return 18;
      case M3ButtonSize.medium:
        return 28;
      case M3ButtonSize.large:
        return 32;
    }
  }

  EdgeInsetsGeometry _getPadding() {
    switch (size) {
      case M3ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case M3ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case M3ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  double _getIconSpacing() {
    switch (size) {
      case M3ButtonSize.small:
        return 6;
      case M3ButtonSize.medium:
        return 8;
      case M3ButtonSize.large:
        return 12;
    }
  }

  double _getMinTouchTarget() {
    switch (size) {
      case M3ButtonSize.small:
        return AppSizes.minTouchTarget;
      case M3ButtonSize.medium:
        return AppSizes.recommendedTouchTarget;
      case M3ButtonSize.large:
        return AppSizes.largeTouchTarget;
    }
  }
}

enum M3ButtonType {
  primary,
  secondary,
  outlined,
  text,
}

enum M3ButtonSize {
  small,
  medium,
  large,
}
