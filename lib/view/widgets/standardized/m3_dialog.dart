import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/app_export.dart';
import '../../../theme/animations.dart';
import '../../../utils/accessibility_utils.dart';
import 'm3_button.dart';

/// Standardized M3 Dialog Components
/// Provides consistent dialog patterns across the app
class M3Dialog {
  M3Dialog._();

  /// Show a confirmation dialog with M3 design
  static Future<bool?> showConfirmation({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    M3DialogType type = M3DialogType.warning,
    bool barrierDismissible = true,
  }) {
    return Get.dialog<bool>(
      _M3ConfirmationDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        type: type,
      ),
      barrierDismissible: barrierDismissible,
      transitionDuration: AppAnimations.medium2,
      transitionCurve: AppAnimations.emphasized,
    );
  }

  /// Show an information dialog with M3 design
  static Future<void> showInfo({
    required String title,
    required String message,
    String buttonText = 'OK',
    M3DialogType type = M3DialogType.info,
    bool barrierDismissible = true,
  }) {
    return Get.dialog(
      _M3InfoDialog(
        title: title,
        message: message,
        buttonText: buttonText,
        type: type,
      ),
      barrierDismissible: barrierDismissible,
      transitionDuration: AppAnimations.medium2,
      transitionCurve: AppAnimations.emphasized,
    );
  }

  /// Show a loading dialog with M3 design
  static void showLoading({
    String message = 'Loading...',
    bool barrierDismissible = false,
  }) {
    Get.dialog(
      _M3LoadingDialog(message: message),
      barrierDismissible: barrierDismissible,
      transitionDuration: AppAnimations.short3,
    );
  }

  /// Hide the loading dialog
  static void hideLoading() {
    if (Get.isDialogOpen ?? false) {
      Get.back();
    }
  }

  /// Show a custom dialog with M3 design
  static Future<T?> showCustom<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
  }) {
    return Get.dialog<T>(
      _M3CustomDialog(child: child),
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      transitionDuration: AppAnimations.medium2,
      transitionCurve: AppAnimations.emphasized,
    );
  }
}

class _M3ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final M3DialogType type;

  const _M3ConfirmationDialog({
    required this.title,
    required this.message,
    required this.confirmText,
    required this.cancelText,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibilityUtils.createFocusTrap(
      child: Dialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getTypeIcon(),
                    color: _getTypeColor(),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: CustomTextStyles.h5.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: CustomTextStyles.b3.copyWith(
                  color: AppColors.darkerGrey,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  M3Button.text(
                    text: cancelText,
                    onPressed: () => Get.back(result: false),
                    size: M3ButtonSize.medium,
                  ),
                  const SizedBox(width: 8),
                  M3Button.primary(
                    text: confirmText,
                    onPressed: () => Get.back(result: true),
                    size: M3ButtonSize.medium,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (type) {
      case M3DialogType.success:
        return Icons.check_circle_outline;
      case M3DialogType.warning:
        return Icons.warning_amber_outlined;
      case M3DialogType.error:
        return Icons.error_outline;
      case M3DialogType.info:
        return Icons.info_outline;
    }
  }

  Color _getTypeColor() {
    switch (type) {
      case M3DialogType.success:
        return AppColors.darkGreen;
      case M3DialogType.warning:
        return AppColors.orange;
      case M3DialogType.error:
        return AppColors.red;
      case M3DialogType.info:
        return AppColors.primary;
    }
  }
}

class _M3InfoDialog extends StatelessWidget {
  final String title;
  final String message;
  final String buttonText;
  final M3DialogType type;

  const _M3InfoDialog({
    required this.title,
    required this.message,
    required this.buttonText,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return AccessibilityUtils.createFocusTrap(
      child: Dialog(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(28),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getTypeIcon(),
                    color: _getTypeColor(),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: CustomTextStyles.h5.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: CustomTextStyles.b3.copyWith(
                  color: AppColors.darkerGrey,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 24),
              Align(
                alignment: Alignment.centerRight,
                child: M3Button.primary(
                  text: buttonText,
                  onPressed: () => Get.back(),
                  size: M3ButtonSize.medium,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (type) {
      case M3DialogType.success:
        return Icons.check_circle_outline;
      case M3DialogType.warning:
        return Icons.warning_amber_outlined;
      case M3DialogType.error:
        return Icons.error_outline;
      case M3DialogType.info:
        return Icons.info_outline;
    }
  }

  Color _getTypeColor() {
    switch (type) {
      case M3DialogType.success:
        return AppColors.darkGreen;
      case M3DialogType.warning:
        return AppColors.orange;
      case M3DialogType.error:
        return AppColors.red;
      case M3DialogType.info:
        return AppColors.primary;
    }
  }
}

class _M3LoadingDialog extends StatelessWidget {
  final String message;

  const _M3LoadingDialog({required this.message});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AccessibilityUtils.accessibleLoadingIndicator(
              label: message,
              child: const CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: CustomTextStyles.b3.copyWith(
                color: AppColors.darkerGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _M3CustomDialog extends StatelessWidget {
  final Widget child;

  const _M3CustomDialog({required this.child});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: child,
    );
  }
}

enum M3DialogType {
  success,
  warning,
  error,
  info,
}
