import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../utils/app_export.dart';
import '../../../theme/animations.dart';
import '../../../utils/accessibility_utils.dart';

/// Standardized M3 Form Field Component
/// Replaces CustomTextFormField and LabelTextField with consistent M3 design
class M3Form<PERSON>ield extends StatefulWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final bool obscureText;
  final bool enabled;
  final bool required;
  final int? maxLines;
  final int? maxLength;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? margin;
  final String? helperText;
  final M3FormFieldSize size;
  final bool showCounter;

  const M3FormField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.obscureText = false,
    this.enabled = true,
    this.required = false,
    this.maxLines = 1,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.inputFormatters,
    this.margin,
    this.helperText,
    this.size = M3FormFieldSize.medium,
    this.showCounter = false,
  });

  // Factory constructors for common field types
  const M3FormField.email({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.margin,
    this.helperText,
    this.size = M3FormFieldSize.medium,
  }) : keyboardType = TextInputType.emailAddress,
       textInputAction = TextInputAction.next,
       obscureText = false,
       maxLines = 1,
       maxLength = null,
       prefixIcon = null,
       suffixIcon = null,
       onTap = null,
       inputFormatters = null,
       showCounter = false;

  const M3FormField.phone({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.margin,
    this.helperText,
    this.size = M3FormFieldSize.medium,
  }) : keyboardType = TextInputType.phone,
       textInputAction = TextInputAction.next,
       obscureText = false,
       maxLines = 1,
       maxLength = 10,
       prefixIcon = null,
       suffixIcon = null,
       onTap = null,
       inputFormatters = null,
       showCounter = false;

  const M3FormField.password({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.margin,
    this.helperText,
    this.size = M3FormFieldSize.medium,
  }) : keyboardType = TextInputType.visiblePassword,
       textInputAction = TextInputAction.done,
       obscureText = true,
       maxLines = 1,
       maxLength = null,
       prefixIcon = null,
       suffixIcon = null,
       onTap = null,
       inputFormatters = null,
       showCounter = false;

  const M3FormField.multiline({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.enabled = true,
    this.required = false,
    this.maxLines = 3,
    this.maxLength,
    this.onChanged,
    this.onSubmitted,
    this.focusNode,
    this.margin,
    this.helperText,
    this.size = M3FormFieldSize.medium,
    this.showCounter = false,
  }) : keyboardType = TextInputType.multiline,
       textInputAction = TextInputAction.newline,
       obscureText = false,
       prefixIcon = null,
       suffixIcon = null,
       onTap = null,
       inputFormatters = null;

  @override
  State<M3FormField> createState() => _M3FormFieldState();
}

class _M3FormFieldState extends State<M3FormField> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _labelAnimation;
  late FocusNode _focusNode;
  bool _hasError = false;
  bool _isFocused = false;
  bool _obscureText = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.short3,
      vsync: this,
    );
    _labelAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: AppAnimations.emphasized),
    );
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.obscureText;
    
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget field = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLabel(theme),
        SizedBox(height: AppSizes.spaceExtraSmall),
        _buildTextField(theme),
        if (widget.helperText != null) ...[
          SizedBox(height: AppSizes.spaceExtraSmall),
          _buildHelperText(theme),
        ],
      ],
    );

    if (widget.margin != null) {
      field = Padding(padding: widget.margin!, child: field);
    }

    return AccessibilityUtils.accessibleFormField(
      child: field,
      label: widget.label,
      hint: widget.hint,
      required: widget.required,
    );
  }

  Widget _buildLabel(ThemeData theme) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: widget.label,
            style: _getLabelStyle(theme),
          ),
          if (widget.required)
            TextSpan(
              text: ' *',
              style: _getLabelStyle(theme).copyWith(color: theme.colorScheme.error),
            ),
        ],
      ),
    );
  }

  Widget _buildTextField(ThemeData theme) {
    return M3AnimatedContainer(
      duration: AppAnimations.short2,
      decoration: BoxDecoration(
        color: _getBackgroundColor(theme),
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: Border.all(
          color: _getBorderColor(theme),
          width: _isFocused ? 2.0 : 1.0,
        ),
        boxShadow: _hasError ? [] : [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        validator: _validator,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        obscureText: _obscureText,
        enabled: widget.enabled,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        inputFormatters: widget.inputFormatters,
        onTap: widget.onTap,
        onChanged: widget.onChanged,
        onFieldSubmitted: widget.onSubmitted,
        style: _getTextStyle(theme),
        decoration: InputDecoration(
          hintText: widget.hint,
          hintStyle: _getHintStyle(theme),
          prefixIcon: widget.prefixIcon,
          suffixIcon: _buildSuffixIcon(),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          contentPadding: _getContentPadding(),
          counterText: widget.showCounter ? null : '',
        ),
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: AppColors.grey,
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }
    return widget.suffixIcon;
  }

  Widget _buildHelperText(ThemeData theme) {
    return Text(
      widget.helperText!,
      style: CustomTextStyles.b6.copyWith(
        color: AppColors.grey,
      ),
    );
  }

  String? _validator(String? value) {
    final result = widget.validator?.call(value);
    setState(() {
      _hasError = result != null;
    });
    return result;
  }

  TextStyle _getLabelStyle(ThemeData theme) {
    return CustomTextStyles.b4.copyWith(
      fontWeight: FontWeight.w600,
      color: AppColors.black,
    );
  }

  TextStyle _getTextStyle(ThemeData theme) {
    return CustomTextStyles.b3.copyWith(
      color: widget.enabled ? AppColors.black : AppColors.grey,
    );
  }

  TextStyle _getHintStyle(ThemeData theme) {
    return CustomTextStyles.b3.copyWith(
      color: AppColors.grey,
    );
  }

  Color _getBackgroundColor(ThemeData theme) {
    if (!widget.enabled) return AppColors.lightGrey;
    if (_hasError) return AppColors.red.withValues(alpha: 0.05);
    return Colors.white;
  }

  Color _getBorderColor(ThemeData theme) {
    if (_hasError) return theme.colorScheme.error;
    if (_isFocused) return AppColors.primary;
    return AppColors.borderSecondary;
  }

  double _getBorderRadius() {
    switch (widget.size) {
      case M3FormFieldSize.small:
        return 8;
      case M3FormFieldSize.medium:
        return 12;
      case M3FormFieldSize.large:
        return 16;
    }
  }

  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case M3FormFieldSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 8);
      case M3FormFieldSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case M3FormFieldSize.large:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
    }
  }
}

enum M3FormFieldSize {
  small,
  medium,
  large,
}
