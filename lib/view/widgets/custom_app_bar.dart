import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    this.elevation,
    this.height,
    this.styleType,
    this.leadingWidth,
    this.leading,
    this.leadingBack = true,
    this.onLeadingTap,
    this.title,
    this.titleWidget,
    this.textStyle,
    this.centerTitle,
    this.actions,
    this.shadowColor,
    this.backgroundColor,
    this.leadingBackColor,
    this.textColor,
  });

  final double? elevation;
  final double? height;
  final Style? styleType;
  final double? leadingWidth;
  final Widget? leading;
  final bool? leadingBack;
  final Function()? onLeadingTap;
  final String? title;
  final Widget? titleWidget;
  final TextStyle? textStyle;
  final bool? centerTitle;
  final List<Widget>? actions;
  final Color? shadowColor;
  final Color? backgroundColor;
  final Color? leadingBackColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppBar(
      elevation: elevation ?? AppSizes.elevationLevel1, // M3 elevation
      toolbarHeight: height ?? AppSizes.appBarHeight,
      automaticallyImplyLeading: false,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface, // M3 surface color
      surfaceTintColor: theme.colorScheme.surfaceTint, // M3 surface tint
      leadingWidth: leadingWidth ?? AppSizes.touchTargetMin, // M3 touch target
      leading: _leading(context),
      title: titleWidget ?? _buildTitle(theme),
      titleSpacing: AppSizes.sm, // Reverted to original spacing
      centerTitle: centerTitle ?? false,
      actions: actions,
      shadowColor: shadowColor ?? theme.colorScheme.shadow, // M3 shadow color
      foregroundColor: theme.colorScheme.onSurface, // M3 foreground color
    );
  }

  Widget _buildTitle(ThemeData theme){
    return Text(
      title ?? '',
      style: textStyle ?? theme.textTheme.titleLarge?.copyWith( // M3 typography
        color: textColor ?? theme.colorScheme.onSurface,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget? _leading(BuildContext context) {
    final theme = Theme.of(context);

    if (leadingBack == false) {
      return Padding(
        padding: EdgeInsets.only(left: AppSizes.md), // Reverted to original spacing
        child: leading,
      );
    } else {
      return GestureDetector(
        onTap: () {
          if(onLeadingTap != null){
            onLeadingTap!(); // Fixed null safety
          }else{
            Navigator.pop(context);
          }
        },
        child: Padding(
          padding: EdgeInsets.only(left: AppSizes.md), // Reverted to original spacing
          child: CustomImageView(
            color: leadingBackColor ?? theme.colorScheme.onSurface, // M3 on-surface color
            imagePath: AppIcons.arrowBack,
            width: AppSizes.iconMd, // M3 icon size
            height: AppSizes.iconMd,
          ),
        ),
      );
    }
  }


  @override
  Size get preferredSize => Size(
    SizeUtils.width,
    height ?? AppSizes.appBarHeight,
  );

}

enum Style {
  bgShadow,
}
