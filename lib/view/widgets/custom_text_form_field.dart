import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import '../../utils/app_export.dart';
import 'standardized/m3_form_field.dart';

class CustomTextForm<PERSON>ield extends StatefulWidget {
  const CustomTextFormField({
    super.key,
    this.alignment,
    this.width,
    this.height,
    this.scrollPadding,
    this.controller,
    this.focusNode,
    this.autofocus = false,
    this.enabled = true,
    this.onChanged,
    this.textStyle,
    this.obscureText = false,
    this.textInputAction = TextInputAction.next,
    this.textInputType = TextInputType.text,
    this.maxLines,
    this.hintText,
    this.hintStyle,
    this.prefix,
    this.prefixConstraints,
    this.suffix,
    this.suffixConstraints,
    this.contentPadding,
    this.borderDecoration,
    this.fillColor,
    this.filled = true,
    this.validator,
    this.onFocusChange,
    this.borderColor,
    this.onTap,
    this.disabledBorderColor,
    this.allowShadow = true,
  });

  final Alignment? alignment;
  final double? width;
  final double? height;
  final TextEditingController? scrollPadding;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool enabled;
  final bool? autofocus;
  final ValueChanged<String>? onChanged;
  final TextStyle? textStyle;
  final bool? obscureText;
  final TextInputAction? textInputAction;
  final TextInputType? textInputType;
  final int? maxLines;
  final String? hintText;
  final TextStyle? hintStyle;
  final Widget? prefix;
  final BoxConstraints? prefixConstraints;
  final Widget? suffix;
  final BoxConstraints? suffixConstraints;
  final EdgeInsets? contentPadding;
  final InputBorder? borderDecoration;
  final Color? fillColor;
  final bool? filled;
  final FormFieldValidator<String>? validator;
  final void Function(bool)? onFocusChange;
  final Color? borderColor;
  final VoidCallback? onTap;
  final Color? disabledBorderColor;
  final bool allowShadow;

  @override
  CustomTextFormFieldState createState() => CustomTextFormFieldState();
}

class CustomTextFormFieldState extends State<CustomTextFormField> {
  late FocusNode focusNode1;
  bool hasError = false;

  @override
  void initState() {
    super.initState();
    focusNode1 = widget.focusNode ?? FocusNode();

    // Add listener to detect focus change
    focusNode1.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    focusNode1.removeListener(() {}); // Remove listener to avoid memory leaks
    if (widget.focusNode == null) {
      focusNode1.dispose(); // Dispose focusNode only if it was created in this widget
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.alignment != null
        ? Align(
      alignment: widget.alignment ?? Alignment.center,
      child: textFormFieldWidget(context),
    )
        : textFormFieldWidget(context);
  }

  Widget textFormFieldWidget(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      width: widget.width ?? (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5),
     height: widget.height,
      child: Container(
        decoration: BoxDecoration(
          boxShadow: hasError
              ? [] // No shadow when there's an error
              : widget.allowShadow
              ? [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withValues(alpha: 0.1), // M3 compliant shadow
                    spreadRadius: 1,
                    blurRadius: AppSizes.elevationLevel2,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
          color: theme.colorScheme.surface, // Use M3 surface color
          borderRadius: BorderRadiusStyle.radiusMd, // M3 compliant border radius
        ),
        child: TextFormField(
          scrollPadding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          controller: widget.controller,
          focusNode: focusNode1,
          onTapOutside: (event) {
            focusNode1.unfocus();
            widget.onFocusChange?.call(false);
          },
          enabled: widget.enabled,
          onChanged: widget.onChanged,
          autofocus: widget.autofocus!,
          style: widget.textStyle ??
              theme.textTheme.bodyLarge?.copyWith( // Use M3 typography
                color: !widget.enabled
                    ? theme.colorScheme.onSurface.withValues(alpha: 0.38) // M3 disabled color
                    : theme.colorScheme.onSurface, // M3 enabled color
                fontWeight: FontWeight.w500,
              ),
          obscureText: widget.obscureText!,
          textInputAction: widget.textInputAction,
          keyboardType: widget.textInputType,
          maxLines: widget.maxLines ?? 1,
          decoration: getDecoration(),
          validator: (value) {
            String? validationResult = widget.validator?.call(value);
            setState(() {
              hasError = validationResult != null; // Toggle shadow based on validation result
            });
            return validationResult;
          },
          onTap: () {
            widget.onFocusChange?.call(true);
            widget.onTap?.call();
          },
          onEditingComplete: () {
            widget.onFocusChange?.call(false);
          },
        ),
      ),
    );
  }

  InputDecoration getDecoration() {
    final theme = Theme.of(context);

    return InputDecoration(
      hintText: widget.hintText ?? "",
      hintStyle: widget.hintStyle ??
          theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6), // M3 hint color
          ),
      prefixIcon: widget.prefix,
      prefixIconConstraints: widget.prefixConstraints,
      suffixIcon: widget.suffix,
      suffixIconConstraints: widget.suffixConstraints,
      isDense: true,
      contentPadding: widget.contentPadding ??
          const EdgeInsets.symmetric(vertical: 18, horizontal: AppSizes.md),
      fillColor: focusNode1.hasFocus || widget.enabled
          ? theme.colorScheme.surface
          : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
      filled: widget.filled,
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadiusStyle.radiusMd, // M3 compliant border radius
        borderSide: BorderSide(
          color: theme.colorScheme.error, // M3 error color
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius:BorderRadiusStyle.border12 ,
        borderSide: const BorderSide(
          color: AppColors.red,
          width: 1,
        ),
      ),
      errorStyle: CustomTextStyles.b6.copyWith(color: Colors.red),
      border: widget.borderDecoration ??
          OutlineInputBorder(
            borderRadius: BorderRadiusStyle.border12,
            borderSide: const BorderSide(
              width: 1,
            ),
          ),
      enabledBorder: widget.borderDecoration ??
          OutlineInputBorder(
            borderRadius: BorderRadiusStyle.border12,
            borderSide: BorderSide(
              color: widget.borderColor ?? Colors.transparent,
              width: 1,
            ),
          ),
      focusedBorder: widget.borderDecoration ??
          OutlineInputBorder(
            borderRadius: BorderRadiusStyle.border12,
            borderSide: BorderSide(
              color: widget.borderColor ?? AppColors.primary,
              width: 1,
            ),
          ),
    );
  }
}