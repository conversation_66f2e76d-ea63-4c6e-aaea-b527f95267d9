import 'package:flutter/material.dart';

import '../../utils/app_export.dart';

class CustomElevatedButton2 extends BaseButton {
  const CustomElevatedButton2({
    super.key,
    this.decoration,
    this.leftIcon,
    this.rightIcon,
    super.margin,
    super.onPressed,
    super.buttonStyle,
    super.alignment,
    super.buttonTextStyle,
    super.isDisabled,
    super.height,
    super.width,
    required super.text,
  });

  final BoxDecoration? decoration;

  final Widget? leftIcon;

  final Widget? rightIcon;

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(
      alignment: alignment ?? Alignment.center,
      child: buildElevatedButtonWidget(context),
    )
        : buildElevatedButtonWidget(context);
  }

  Widget buildElevatedButtonWidget(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: height ?? AppSizes.buttonMedium, // M3 compliant button height
      width: width ?? 142,
      margin: margin,
      decoration: decoration,
      child: ElevatedButton(
        style: buttonStyle ?? ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary, // M3 primary color
          foregroundColor: theme.colorScheme.onPrimary, // M3 on-primary color
          elevation: AppSizes.elevationLevel1, // M3 elevation
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusLg), // M3 border radius
          ),
          padding: EdgeInsets.symmetric(
            vertical: AppSizes.sm2, // Reverted to original spacing
            horizontal: AppSizes.md,
          ),
          minimumSize: Size(0, AppSizes.touchTargetMin), // M3 minimum touch target
          textStyle: theme.textTheme.labelLarge?.copyWith( // M3 typography
            fontWeight: FontWeight.w600,
            letterSpacing: 0.1,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        onPressed: isDisabled ?? false ? null : onPressed ?? () {},
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            leftIcon ?? const SizedBox.shrink(),
            if (leftIcon != null) SizedBox(width: AppSizes.sm), // Reverted to original spacing
            Flexible(
              child: Text(
                text,
                style: buttonTextStyle ??
                    theme.textTheme.labelLarge?.copyWith(
                      color: theme.colorScheme.onPrimary, // M3 on-primary color
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.1,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (rightIcon != null) SizedBox(width: AppSizes.sm), // Reverted to original spacing
            rightIcon ?? const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
