import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:get/get.dart';
import '../../theme/animations.dart';

/// Optimized image widget with performance enhancements and M3 design
class OptimizedImageView extends StatelessWidget {
  final String imagePath;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final BorderRadius? borderRadius;
  final bool enableFullScreen;
  final String? heroTag;
  final Color? backgroundColor;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImageView({
    super.key,
    required this.imagePath,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.enableFullScreen = false,
    this.heroTag,
    this.backgroundColor,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildImageWidget();

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    if (heroTag != null) {
      imageWidget = Hero(
        tag: heroTag!,
        child: imageWidget,
      );
    }

    if (enableFullScreen) {
      imageWidget = M3InteractiveButton(
        onPressed: () => _showFullScreen(context),
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildImageWidget() {
    if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
      return CachedNetworkImage(
        imageUrl: imagePath,
        height: height,
        width: width,
        fit: fit,
        placeholder: (context, url) => placeholder ?? _buildPlaceholder(),
        errorWidget: (context, url, error) => errorWidget ?? _buildErrorWidget(),
        memCacheHeight: (height != null && height!.isFinite) ? height!.toInt() : null,
        memCacheWidth: (width != null && width!.isFinite) ? width!.toInt() : null,
        maxHeightDiskCache: 1000,
        maxWidthDiskCache: 1000,
        fadeInDuration: AppAnimations.short2,
        fadeOutDuration: AppAnimations.short1,
      );
    } else {
      return Image.asset(
        imagePath,
        height: height,
        width: width,
        fit: fit,
        errorBuilder: (context, error, stackTrace) => errorWidget ?? _buildErrorWidget(),
      );
    }
  }

  Widget _buildPlaceholder() {
    return Container(
      height: height,
      width: width,
      color: backgroundColor ?? Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      height: height,
      width: width,
      color: backgroundColor ?? Colors.grey[300],
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image, color: Colors.grey, size: 32),
          SizedBox(height: 8),
          Text(
            'Image not available',
            style: TextStyle(color: Colors.grey, fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFullScreen(BuildContext context) {
    Get.to(
      () => OptimizedFullScreenImage(
        imagePath: imagePath,
        heroTag: heroTag,
      ),
      transition: Transition.fade,
      duration: AppAnimations.medium2,
    );
  }
}

/// Optimized full-screen image viewer with zoom capabilities
class OptimizedFullScreenImage extends StatelessWidget {
  final String imagePath;
  final String? heroTag;

  const OptimizedFullScreenImage({
    super.key,
    required this.imagePath,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Center(
            child: heroTag != null
                ? Hero(
                    tag: heroTag!,
                    child: _buildPhotoView(),
                  )
                : _buildPhotoView(),
          ),
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: M3InteractiveButton(
              onPressed: () => Get.back(),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoView() {
    if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
      return PhotoView(
        imageProvider: CachedNetworkImageProvider(imagePath),
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.covered * 3,
        backgroundDecoration: const BoxDecoration(color: Colors.black),
        loadingBuilder: (context, event) => const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
        errorBuilder: (context, error, stackTrace) => const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, color: Colors.white, size: 48),
              SizedBox(height: 16),
              Text(
                'Failed to load image',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    } else {
      return PhotoView(
        imageProvider: AssetImage(imagePath),
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.covered * 3,
        backgroundDecoration: const BoxDecoration(color: Colors.black),
      );
    }
  }
}

/// Grid view optimized for images with lazy loading
class OptimizedImageGrid extends StatelessWidget {
  final List<String> imageUrls;
  final int crossAxisCount;
  final double aspectRatio;
  final double spacing;
  final EdgeInsetsGeometry? padding;
  final bool enableFullScreen;

  const OptimizedImageGrid({
    super.key,
    required this.imageUrls,
    this.crossAxisCount = 2,
    this.aspectRatio = 1.0,
    this.spacing = 8.0,
    this.padding,
    this.enableFullScreen = true,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: aspectRatio,
      ),
      itemCount: imageUrls.length,
      itemBuilder: (context, index) {
        return OptimizedImageView(
          imagePath: imageUrls[index],
          fit: BoxFit.cover,
          borderRadius: BorderRadius.circular(8),
          enableFullScreen: enableFullScreen,
          heroTag: 'image_$index',
        );
      },
    );
  }
}

/// Staggered image grid for better visual appeal
class StaggeredImageGrid extends StatelessWidget {
  final List<String> imageUrls;
  final EdgeInsetsGeometry? padding;

  const StaggeredImageGrid({
    super.key,
    required this.imageUrls,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        children: List.generate(
          (imageUrls.length / 2).ceil(),
          (rowIndex) {
            final startIndex = rowIndex * 2;
            final endIndex = (startIndex + 2).clamp(0, imageUrls.length);
            final rowImages = imageUrls.sublist(startIndex, endIndex);

            return StaggeredAnimationHelper.buildStaggeredAnimation(
              index: rowIndex,
              child: Padding(
                padding: EdgeInsets.only(bottom: rowIndex < (imageUrls.length / 2).ceil() - 1 ? 8.0 : 0),
                child: Row(
                  children: rowImages.asMap().entries.map((entry) {
                    final index = entry.key;
                    final imageUrl = entry.value;
                    
                    return Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: index < rowImages.length - 1 ? 8.0 : 0),
                        child: OptimizedImageView(
                          imagePath: imageUrl,
                          height: 120,
                          fit: BoxFit.cover,
                          borderRadius: BorderRadius.circular(8),
                          enableFullScreen: true,
                          heroTag: 'staggered_image_${startIndex + index}',
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
