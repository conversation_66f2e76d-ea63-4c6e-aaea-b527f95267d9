import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';

class CustomDropdown extends StatefulWidget {
  final String hintText;
  final List<String> items;
  final ValueChanged<String?> onChanged;
  final double? dropdownWidth;
  final double? buttonWidth;
  final String? selectedValue;

  const CustomDropdown({
    super.key,
    required this.hintText,
    required this.items,
    required this.onChanged,
    this.dropdownWidth,
    this.buttonWidth,
    this.selectedValue,
  });

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  String? selectedOption;
  bool isDropdownOpened = false;

  @override
  void initState() {
    super.initState();
    selectedOption = widget.selectedValue;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: AppSizes.formFieldLarge, // M3 compliant height
      width: widget.buttonWidth ?? MediaQuery.of(context).size.width, // Button width
      decoration: BoxDecoration(
        color: theme.colorScheme.surface, // M3 surface color
        borderRadius: BorderRadius.circular(AppSizes.radiusMd), // M3 compliant border radius
        border: Border.all(
          color: isDropdownOpened
              ? theme.colorScheme.primary
              : theme.colorScheme.outline.withValues(alpha: 0.5), // M3 outline color
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1), // M3 compliant shadow
            spreadRadius: 1,
            blurRadius: AppSizes.elevationLevel2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<String>(
          isExpanded: true,
          items: widget.items.map((item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 14), // Reverted to original spacing
                    child: Text(
                      item,
                      style: theme.textTheme.bodyLarge?.copyWith( // M3 typography
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                  if(widget.items.last != item )
                    Container(
                      height: 1,
                      color: theme.colorScheme.outline.withValues(alpha: 0.2), // M3 divider color
                    )
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              selectedOption = value; // Update the selected option
            });
            widget.onChanged(selectedOption);
          },
          hint: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16), // Reverted to original spacing
            child: Text(
              selectedOption ?? widget.hintText,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: selectedOption != null
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6), // M3 hint color
              ),
            ),
          ),
          iconStyleData: IconStyleData(
            icon: Padding(
              padding: const EdgeInsets.only(right: 12), // Reverted to original spacing
              child: Icon(
                isDropdownOpened
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: theme.colorScheme.onSurface, // M3 icon color
                size: AppSizes.iconMd, // M3 icon size
              ),
            ),
          ),
          onMenuStateChange: (isOpen) {
            setState(() {
              isDropdownOpened = isOpen; // Update the dropdown open state
            });
          },
          dropdownStyleData: DropdownStyleData(
             //width: widget.dropdownWidth ?? MediaQuery.of(context).size.width - 32,
            width: widget.dropdownWidth ?? widget.buttonWidth,
            maxHeight: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}